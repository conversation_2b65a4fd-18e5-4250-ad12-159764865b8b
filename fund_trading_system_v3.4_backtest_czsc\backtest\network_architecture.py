import tensorflow as tf
from tensorflow.keras.models import Sequential, Model
from tensorflow.keras.layers import (Dense, LSTM, GRU, Dropout, BatchNormalization, 
                                   Input, MultiHeadAttention, LayerNormalization,
                                   GlobalAveragePooling1D, Conv1D, MaxPooling1D,
                                   Bidirectional, Attention, RepeatVector, TimeDistributed)
from tensorflow.keras.optimizers import Adam, RMSprop
from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau, ModelCheckpoint
from typing import Tuple, Dict, List, Optional
import numpy as np


class NetworkArchitecture:
    """
    神经网络架构模块
    构建适合时间序列的神经网络结构
    """
    
    def __init__(self, input_shape: Tuple[int, int], output_dim: int = 1):
        """
        初始化网络架构构建器
        
        Args:
            input_shape: 输入形状 (sequence_length, n_features)
            output_dim: 输出维度
        """
        self.input_shape = input_shape
        self.output_dim = output_dim
        self.model_configs = {}
        self.architecture_log = []
        
    def build_lstm_model(self, lstm_units: List[int] = [64, 32], 
                        dropout_rate: float = 0.2, 
                        bidirectional: bool = False,
                        activation: str = 'tanh') -> Sequential:
        """
        构建LSTM网络
        
        Args:
            lstm_units: LSTM层单元数列表
            dropout_rate: Dropout比率
            bidirectional: 是否使用双向LSTM
            activation: 激活函数
            
        Returns:
            LSTM模型
        """
        model = Sequential()
        
        # 第一层LSTM
        if bidirectional:
            model.add(Bidirectional(
                LSTM(lstm_units[0], return_sequences=len(lstm_units) > 1, 
                     activation=activation, input_shape=self.input_shape)))
        else:
            model.add(LSTM(lstm_units[0], return_sequences=len(lstm_units) > 1,
                          activation=activation, input_shape=self.input_shape))
        
        model.add(BatchNormalization())
        model.add(Dropout(dropout_rate))
        
        # 额外的LSTM层
        for i, units in enumerate(lstm_units[1:], 1):
            return_sequences = i < len(lstm_units) - 1
            
            if bidirectional:
                model.add(Bidirectional(
                    LSTM(units, return_sequences=return_sequences, activation=activation)))
            else:
                model.add(LSTM(units, return_sequences=return_sequences, activation=activation))
            
            model.add(BatchNormalization())
            model.add(Dropout(dropout_rate))
        
        # 输出层
        model.add(Dense(64, activation='relu'))
        model.add(Dropout(dropout_rate))
        model.add(Dense(self.output_dim, activation='linear'))
        
        self.architecture_log.append(f"Built LSTM model with units: {lstm_units}, bidirectional: {bidirectional}")
        
        return model
    
    def build_gru_model(self, gru_units: List[int] = [64, 32],
                       dropout_rate: float = 0.2,
                       bidirectional: bool = False) -> Sequential:
        """
        构建GRU网络
        
        Args:
            gru_units: GRU层单元数列表
            dropout_rate: Dropout比率
            bidirectional: 是否使用双向GRU
            
        Returns:
            GRU模型
        """
        model = Sequential()
        
        # 第一层GRU
        if bidirectional:
            model.add(Bidirectional(
                GRU(gru_units[0], return_sequences=len(gru_units) > 1,
                    input_shape=self.input_shape)))
        else:
            model.add(GRU(gru_units[0], return_sequences=len(gru_units) > 1,
                         input_shape=self.input_shape))
        
        model.add(BatchNormalization())
        model.add(Dropout(dropout_rate))
        
        # 额外的GRU层
        for i, units in enumerate(gru_units[1:], 1):
            return_sequences = i < len(gru_units) - 1
            
            if bidirectional:
                model.add(Bidirectional(GRU(units, return_sequences=return_sequences)))
            else:
                model.add(GRU(units, return_sequences=return_sequences))
            
            model.add(BatchNormalization())
            model.add(Dropout(dropout_rate))
        
        # 输出层
        model.add(Dense(64, activation='relu'))
        model.add(Dropout(dropout_rate))
        model.add(Dense(self.output_dim, activation='linear'))
        
        self.architecture_log.append(f"Built GRU model with units: {gru_units}, bidirectional: {bidirectional}")
        
        return model
    
    def build_cnn_lstm_model(self, cnn_filters: List[int] = [64, 32],
                            kernel_size: int = 3,
                            lstm_units: int = 50,
                            dropout_rate: float = 0.2) -> Sequential:
        """
        构建CNN-LSTM混合模型
        
        Args:
            cnn_filters: CNN滤波器数量列表
            kernel_size: 卷积核大小
            lstm_units: LSTM单元数
            dropout_rate: Dropout比率
            
        Returns:
            CNN-LSTM模型
        """
        model = Sequential()
        
        # CNN层
        model.add(Conv1D(filters=cnn_filters[0], kernel_size=kernel_size, 
                        activation='relu', input_shape=self.input_shape))
        model.add(MaxPooling1D(pool_size=2))
        model.add(Dropout(dropout_rate))
        
        for filters in cnn_filters[1:]:
            model.add(Conv1D(filters=filters, kernel_size=kernel_size, activation='relu'))
            model.add(MaxPooling1D(pool_size=2))
            model.add(Dropout(dropout_rate))
        
        # LSTM层
        model.add(LSTM(lstm_units, dropout=dropout_rate))
        model.add(BatchNormalization())
        
        # 输出层
        model.add(Dense(50, activation='relu'))
        model.add(Dropout(dropout_rate))
        model.add(Dense(self.output_dim, activation='linear'))
        
        self.architecture_log.append(f"Built CNN-LSTM model with CNN filters: {cnn_filters}, LSTM units: {lstm_units}")
        
        return model
    
    def build_attention_lstm_model(self, lstm_units: int = 64,
                                  attention_units: int = 32,
                                  dropout_rate: float = 0.2) -> Model:
        """
        构建带注意力机制的LSTM模型
        
        Args:
            lstm_units: LSTM单元数
            attention_units: 注意力单元数
            dropout_rate: Dropout比率
            
        Returns:
            注意力LSTM模型
        """
        # 输入层
        inputs = Input(shape=self.input_shape)
        
        # LSTM层（返回序列）
        lstm_out = LSTM(lstm_units, return_sequences=True, dropout=dropout_rate)(inputs)
        lstm_out = BatchNormalization()(lstm_out)
        
        # 注意力层
        attention = Dense(attention_units, activation='tanh')(lstm_out)
        attention = Dense(1, activation='softmax')(attention)
        attention = tf.keras.layers.Flatten()(attention)
        attention = RepeatVector(lstm_units)(attention)
        attention = tf.keras.layers.Permute([2, 1])(attention)
        
        # 应用注意力权重
        weighted_input = tf.keras.layers.Multiply()([lstm_out, attention])
        output_attention = GlobalAveragePooling1D()(weighted_input)
        
        # 输出层
        output = Dense(64, activation='relu')(output_attention)
        output = Dropout(dropout_rate)(output)
        output = Dense(self.output_dim, activation='linear')(output)
        
        model = Model(inputs=inputs, outputs=output)
        
        self.architecture_log.append(f"Built Attention-LSTM model with LSTM units: {lstm_units}, attention units: {attention_units}")
        
        return model
    
    def build_transformer_model(self, d_model: int = 64, num_heads: int = 8,
                               num_layers: int = 2, ff_dim: int = 128,
                               dropout_rate: float = 0.1) -> Model:
        """
        构建Transformer模型
        
        Args:
            d_model: 模型维度
            num_heads: 注意力头数
            num_layers: Transformer层数
            ff_dim: 前馈网络维度
            dropout_rate: Dropout比率
            
        Returns:
            Transformer模型
        """
        inputs = Input(shape=self.input_shape)
        
        # 线性投影到d_model维度
        x = Dense(d_model)(inputs)
        
        # 位置编码（简化版）
        positions = tf.range(start=0, limit=self.input_shape[0], delta=1)
        positions = tf.cast(positions, tf.float32)
        position_embedding = Dense(d_model)(tf.expand_dims(positions, 0))
        x = x + position_embedding
        
        # Transformer编码器层
        for _ in range(num_layers):
            # 多头自注意力
            attention_output = MultiHeadAttention(
                num_heads=num_heads, key_dim=d_model//num_heads)(x, x)
            attention_output = Dropout(dropout_rate)(attention_output)
            x1 = LayerNormalization()(x + attention_output)
            
            # 前馈网络
            ff_output = Dense(ff_dim, activation='relu')(x1)
            ff_output = Dense(d_model)(ff_output)
            ff_output = Dropout(dropout_rate)(ff_output)
            x = LayerNormalization()(x1 + ff_output)
        
        # 全局平均池化
        x = GlobalAveragePooling1D()(x)
        
        # 输出层
        x = Dense(64, activation='relu')(x)
        x = Dropout(dropout_rate)(x)
        outputs = Dense(self.output_dim, activation='linear')(x)
        
        model = Model(inputs=inputs, outputs=outputs)
        
        self.architecture_log.append(f"Built Transformer model with d_model: {d_model}, num_heads: {num_heads}, num_layers: {num_layers}")
        
        return model
    
    def build_ensemble_model(self, model_types: List[str] = ['lstm', 'gru', 'cnn_lstm'],
                           voting_method: str = 'average') -> Model:
        """
        构建集成模型
        
        Args:
            model_types: 模型类型列表
            voting_method: 投票方法 ('average', 'weighted')
            
        Returns:
            集成模型
        """
        inputs = Input(shape=self.input_shape)
        model_outputs = []
        
        for model_type in model_types:
            if model_type == 'lstm':
                x = LSTM(64, dropout=0.2)(inputs)
            elif model_type == 'gru':
                x = GRU(64, dropout=0.2)(inputs)
            elif model_type == 'cnn_lstm':
                x = Conv1D(filters=64, kernel_size=3, activation='relu')(inputs)
                x = MaxPooling1D(pool_size=2)(x)
                x = LSTM(32, dropout=0.2)(x)
            
            x = BatchNormalization()(x)
            x = Dense(32, activation='relu')(x)
            x = Dropout(0.2)(x)
            output = Dense(self.output_dim, activation='linear', name=f'{model_type}_output')(x)
            model_outputs.append(output)
        
        # 集成输出
        if voting_method == 'average':
            final_output = tf.keras.layers.Average()(model_outputs)
        else:  # weighted average
            # 简化的加权平均（权重可以通过训练学习）
            weights = [Dense(1, activation='softmax', name=f'weight_{i}')(output) 
                      for i, output in enumerate(model_outputs)]
            weighted_outputs = [tf.keras.layers.Multiply()([output, weight]) 
                               for output, weight in zip(model_outputs, weights)]
            final_output = tf.keras.layers.Add()(weighted_outputs)
        
        model = Model(inputs=inputs, outputs=final_output)
        
        self.architecture_log.append(f"Built ensemble model with types: {model_types}, voting: {voting_method}")
        
        return model
    
    def compile_model(self, model: Model, learning_rate: float = 0.001,
                     optimizer: str = 'adam', loss: str = 'mse',
                     metrics: List[str] = ['mae']) -> Model:
        """
        编译模型
        
        Args:
            model: 要编译的模型
            learning_rate: 学习率
            optimizer: 优化器
            loss: 损失函数
            metrics: 评估指标
            
        Returns:
            编译后的模型
        """
        if optimizer == 'adam':
            opt = Adam(learning_rate=learning_rate)
        elif optimizer == 'rmsprop':
            opt = RMSprop(learning_rate=learning_rate)
        else:
            opt = optimizer
        
        model.compile(optimizer=opt, loss=loss, metrics=metrics)
        
        self.architecture_log.append(f"Compiled model with optimizer: {optimizer}, loss: {loss}")
        
        return model
    
    def get_callbacks(self, patience: int = 10, min_delta: float = 0.001,
                     reduce_lr_patience: int = 5, checkpoint_path: Optional[str] = None) -> List:
        """
        获取训练回调函数
        
        Args:
            patience: 早停耐心值
            min_delta: 最小改善阈值
            reduce_lr_patience: 学习率减少耐心值
            checkpoint_path: 模型保存路径
            
        Returns:
            回调函数列表
        """
        callbacks = []
        
        # 早停
        early_stopping = EarlyStopping(
            monitor='val_loss',
            patience=patience,
            min_delta=min_delta,
            restore_best_weights=True,
            verbose=1
        )
        callbacks.append(early_stopping)
        
        # 学习率衰减
        reduce_lr = ReduceLROnPlateau(
            monitor='val_loss',
            factor=0.5,
            patience=reduce_lr_patience,
            min_lr=1e-7,
            verbose=1
        )
        callbacks.append(reduce_lr)
        
        # 模型检查点
        if checkpoint_path:
            checkpoint = ModelCheckpoint(
                checkpoint_path,
                monitor='val_loss',
                save_best_only=True,
                save_weights_only=False,
                verbose=1
            )
            callbacks.append(checkpoint)
        
        return callbacks
    
    def create_model_config(self, model_type: str, **kwargs) -> Dict:
        """
        创建模型配置
        
        Args:
            model_type: 模型类型
            **kwargs: 模型参数
            
        Returns:
            模型配置字典
        """
        config = {
            'model_type': model_type,
            'input_shape': self.input_shape,
            'output_dim': self.output_dim,
            'parameters': kwargs,
            'architecture_log': self.architecture_log.copy()
        }
        
        self.model_configs[model_type] = config
        
        return config 