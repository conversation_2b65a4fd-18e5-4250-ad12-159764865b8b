import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any
from sklearn.model_selection import TimeSeriesSplit
from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score
from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
from sklearn.linear_model import LinearRegression, Ridge, Lasso
import warnings
warnings.filterwarnings('ignore')

# 尝试导入torch，如果不可用则使用sklearn
try:
    import torch
    import torch.nn as nn
    import torch.optim as optim
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False

# 尝试导入optuna，如果不可用则跳过超参数优化
try:
    import optuna
    OPTUNA_AVAILABLE = True
except ImportError:
    OPTUNA_AVAILABLE = False


class ModelTrainer:
    """
    模型训练验证模块
    负责模型训练、交叉验证、超参数优化
    """
    
    def __init__(self, validation_split: float = 0.2, early_stopping_patience: int = 10,
                 random_state: int = 42):
        """
        初始化模型训练器
        
        Args:
            validation_split: 验证集比例
            early_stopping_patience: 早停耐心值
            random_state: 随机种子
        """
        self.validation_split = validation_split
        self.early_stopping_patience = early_stopping_patience
        self.random_state = random_state
        self.training_history = {}
        self.cv_results = {}
        self.best_params = {}
        self.training_log = []
        
        # 设置随机种子
        np.random.seed(random_state)
        if TORCH_AVAILABLE:
            torch.manual_seed(random_state)
    
    def train_with_validation(self, model, X_train: np.ndarray, y_train: np.ndarray,
                            X_val: np.ndarray, y_val: np.ndarray,
                            epochs: int = 100, batch_size: int = 32,
                            callbacks: Optional[List] = None, verbose: int = 1) -> Dict:
        """
        带验证集的模型训练
        
        Args:
            model: 要训练的模型
            X_train: 训练特征
            y_train: 训练目标
            X_val: 验证特征
            y_val: 验证目标
            epochs: 训练轮数
            batch_size: 批大小
            callbacks: 回调函数列表
            verbose: 详细程度
            
        Returns:
            训练历史
        """
        self.training_log.append(f"Starting training with {len(X_train)} training samples, {len(X_val)} validation samples")
        
        # 训练模型
        history = model.fit(
            X_train, y_train,
            validation_data=(X_val, y_val),
            epochs=epochs,
            batch_size=batch_size,
            callbacks=callbacks,
            verbose=verbose,
            shuffle=False  # 时间序列数据不随机打乱
        )
        
        # 保存训练历史
        model_name = f"model_{len(self.training_history)}"
        self.training_history[model_name] = {
            'history': history.history,
            'epochs_trained': len(history.history['loss']),
            'final_train_loss': history.history['loss'][-1],
            'final_val_loss': history.history['val_loss'][-1],
            'best_val_loss': min(history.history['val_loss']),
            'best_epoch': np.argmin(history.history['val_loss']) + 1
        }
        
        self.training_log.append(f"Training completed. Best validation loss: {self.training_history[model_name]['best_val_loss']:.6f} at epoch {self.training_history[model_name]['best_epoch']}")
        
        return history
    
    def cross_validate(self, model_builder, X: np.ndarray, y: np.ndarray,
                      cv_folds: int = 5, **model_params) -> Dict:
        """
        时间序列交叉验证
        
        Args:
            model_builder: 模型构建函数
            X: 特征数据
            y: 目标数据
            cv_folds: 交叉验证折数
            **model_params: 模型参数
            
        Returns:
            交叉验证结果
        """
        self.training_log.append(f"Starting {cv_folds}-fold time series cross validation")
        
        # 使用时间序列分割
        tscv = TimeSeriesSplit(n_splits=cv_folds)
        
        cv_scores = {
            'train_mse': [],
            'val_mse': [],
            'train_mae': [],
            'val_mae': [],
            'train_r2': [],
            'val_r2': []
        }
        
        fold_results = []
        
        for fold, (train_idx, val_idx) in enumerate(tscv.split(X)):
            self.training_log.append(f"Training fold {fold + 1}/{cv_folds}")
            
            # 分割数据
            X_train_fold, X_val_fold = X[train_idx], X[val_idx]
            y_train_fold, y_val_fold = y[train_idx], y[val_idx]
            
            # 构建并训练模型
            model = model_builder(**model_params)
            
            # 训练模型（简化版，无早停）
            history = model.fit(
                X_train_fold, y_train_fold,
                validation_data=(X_val_fold, y_val_fold),
                epochs=50,  # 较少的epoch用于交叉验证
                batch_size=32,
                verbose=0,
                shuffle=False
            )
            
            # 预测
            train_pred = model.predict(X_train_fold, verbose=0)
            val_pred = model.predict(X_val_fold, verbose=0)
            
            # 计算指标
            train_mse = mean_squared_error(y_train_fold, train_pred)
            val_mse = mean_squared_error(y_val_fold, val_pred)
            train_mae = mean_absolute_error(y_train_fold, train_pred)
            val_mae = mean_absolute_error(y_val_fold, val_pred)
            train_r2 = r2_score(y_train_fold, train_pred)
            val_r2 = r2_score(y_val_fold, val_pred)
            
            # 保存结果
            cv_scores['train_mse'].append(train_mse)
            cv_scores['val_mse'].append(val_mse)
            cv_scores['train_mae'].append(train_mae)
            cv_scores['val_mae'].append(val_mae)
            cv_scores['train_r2'].append(train_r2)
            cv_scores['val_r2'].append(val_r2)
            
            fold_results.append({
                'fold': fold + 1,
                'train_mse': train_mse,
                'val_mse': val_mse,
                'train_mae': train_mae,
                'val_mae': val_mae,
                'train_r2': train_r2,
                'val_r2': val_r2
            })
        
        # 计算统计信息
        cv_summary = {}
        for metric in cv_scores:
            cv_summary[metric] = {
                'mean': np.mean(cv_scores[metric]),
                'std': np.std(cv_scores[metric]),
                'min': np.min(cv_scores[metric]),
                'max': np.max(cv_scores[metric])
            }
        
        cv_results = {
            'cv_summary': cv_summary,
            'fold_results': fold_results,
            'cv_scores': cv_scores
        }
        
        self.cv_results = cv_results
        self.training_log.append(f"Cross validation completed. Mean validation MSE: {cv_summary['val_mse']['mean']:.6f} ± {cv_summary['val_mse']['std']:.6f}")
        
        return cv_results
    
    def hyperparameter_tuning(self, model_builder, X_train: np.ndarray, y_train: np.ndarray,
                             X_val: np.ndarray, y_val: np.ndarray,
                             param_space: Dict, n_trials: int = 100,
                             direction: str = 'minimize') -> Dict:
        """
        超参数优化
        
        Args:
            model_builder: 模型构建函数
            X_train: 训练特征
            y_train: 训练目标
            X_val: 验证特征
            y_val: 验证目标
            param_space: 参数空间定义
            n_trials: 试验次数
            direction: 优化方向
            
        Returns:
            最佳参数和优化结果
        """
        self.training_log.append(f"Starting hyperparameter tuning with {n_trials} trials")
        
        def objective(trial):
            # 从参数空间中采样参数
            params = {}
            for param_name, param_config in param_space.items():
                if param_config['type'] == 'int':
                    params[param_name] = trial.suggest_int(
                        param_name, param_config['low'], param_config['high'])
                elif param_config['type'] == 'float':
                    params[param_name] = trial.suggest_float(
                        param_name, param_config['low'], param_config['high'])
                elif param_config['type'] == 'categorical':
                    params[param_name] = trial.suggest_categorical(
                        param_name, param_config['choices'])
            
            # 构建模型
            model = model_builder(**params)
            
            # 训练模型
            history = model.fit(
                X_train, y_train,
                validation_data=(X_val, y_val),
                epochs=30,  # 较少的epoch用于超参数搜索
                batch_size=32,
                verbose=0,
                shuffle=False,
                callbacks=[TFKerasPruningCallback(trial, 'val_loss')]
            )
            
            # 返回验证损失
            return min(history.history['val_loss'])
        
        # 创建研究对象
        study = optuna.create_study(direction=direction)
        study.optimize(objective, n_trials=n_trials)
        
        # 保存最佳参数
        self.best_params = study.best_params
        
        tuning_results = {
            'best_params': study.best_params,
            'best_value': study.best_value,
            'n_trials': len(study.trials),
            'study': study
        }
        
        self.training_log.append(f"Hyperparameter tuning completed. Best validation loss: {study.best_value:.6f}")
        self.training_log.append(f"Best parameters: {study.best_params}")
        
        return tuning_results
    
    def evaluate_model(self, model, X_test: np.ndarray, y_test: np.ndarray,
                      scaler=None) -> Dict:
        """
        评估模型性能
        
        Args:
            model: 训练好的模型
            X_test: 测试特征
            y_test: 测试目标
            scaler: 标准化器（用于反变换）
            
        Returns:
            评估结果
        """
        # 预测
        y_pred = model.predict(X_test, verbose=0)
        
        # 如果有标准化器，进行反变换
        if scaler is not None:
            y_test_original = scaler.inverse_transform(y_test.reshape(-1, 1)).flatten()
            y_pred_original = scaler.inverse_transform(y_pred.reshape(-1, 1)).flatten()
        else:
            y_test_original = y_test
            y_pred_original = y_pred.flatten()
        
        # 计算评估指标
        mse = mean_squared_error(y_test_original, y_pred_original)
        rmse = np.sqrt(mse)
        mae = mean_absolute_error(y_test_original, y_pred_original)
        r2 = r2_score(y_test_original, y_pred_original)
        
        # 计算方向准确率（对于金融数据很重要）
        direction_accuracy = np.mean(
            (np.sign(y_pred_original[1:] - y_pred_original[:-1]) == 
             np.sign(y_test_original[1:] - y_test_original[:-1]))
        )
        
        # 计算最大误差
        max_error = np.max(np.abs(y_test_original - y_pred_original))
        
        evaluation_results = {
            'mse': mse,
            'rmse': rmse,
            'mae': mae,
            'r2_score': r2,
            'direction_accuracy': direction_accuracy,
            'max_error': max_error,
            'predictions': y_pred_original,
            'actual': y_test_original
        }
        
        self.training_log.append(f"Model evaluation completed - RMSE: {rmse:.6f}, MAE: {mae:.6f}, R²: {r2:.6f}")
        
        return evaluation_results
    
    def compare_models(self, models: Dict[str, Any], X_test: np.ndarray,
                      y_test: np.ndarray, scaler=None) -> pd.DataFrame:
        """
        比较多个模型的性能
        
        Args:
            models: 模型字典 {name: model}
            X_test: 测试特征
            y_test: 测试目标
            scaler: 标准化器
            
        Returns:
            模型比较结果DataFrame
        """
        comparison_results = []
        
        for model_name, model in models.items():
            self.training_log.append(f"Evaluating model: {model_name}")
            
            eval_results = self.evaluate_model(model, X_test, y_test, scaler)
            
            comparison_results.append({
                'model_name': model_name,
                'mse': eval_results['mse'],
                'rmse': eval_results['rmse'],
                'mae': eval_results['mae'],
                'r2_score': eval_results['r2_score'],
                'direction_accuracy': eval_results['direction_accuracy'],
                'max_error': eval_results['max_error']
            })
        
        comparison_df = pd.DataFrame(comparison_results)
        comparison_df = comparison_df.sort_values('rmse')  # 按RMSE排序
        
        self.training_log.append("Model comparison completed")
        
        return comparison_df
    
    def learning_curve_analysis(self, model_builder, X: np.ndarray, y: np.ndarray,
                               train_sizes: List[float] = [0.1, 0.3, 0.5, 0.7, 0.9],
                               **model_params) -> Dict:
        """
        学习曲线分析
        
        Args:
            model_builder: 模型构建函数
            X: 特征数据
            y: 目标数据
            train_sizes: 训练集大小比例列表
            **model_params: 模型参数
            
        Returns:
            学习曲线结果
        """
        learning_curve_results = {
            'train_sizes': [],
            'train_scores': [],
            'val_scores': []
        }
        
        for train_size in train_sizes:
            self.training_log.append(f"Training with {train_size*100:.0f}% of data")
            
            # 计算训练集大小
            n_samples = int(len(X) * train_size)
            
            # 分割数据（保持时间顺序）
            split_point = int(n_samples * 0.8)
            X_train_lc = X[:split_point]
            y_train_lc = y[:split_point]
            X_val_lc = X[split_point:n_samples]
            y_val_lc = y[split_point:n_samples]
            
            if len(X_val_lc) == 0:  # 避免验证集为空
                continue
            
            # 构建并训练模型
            model = model_builder(**model_params)
            
            history = model.fit(
                X_train_lc, y_train_lc,
                validation_data=(X_val_lc, y_val_lc),
                epochs=30,
                batch_size=32,
                verbose=0,
                shuffle=False
            )
            
            # 记录结果
            learning_curve_results['train_sizes'].append(n_samples)
            learning_curve_results['train_scores'].append(min(history.history['loss']))
            learning_curve_results['val_scores'].append(min(history.history['val_loss']))
        
        self.training_log.append("Learning curve analysis completed")
        
        return learning_curve_results
    
    def get_training_summary(self) -> Dict:
        """
        获取训练总结
        
        Returns:
            训练总结字典
        """
        summary = {
            'training_history': self.training_history,
            'cv_results': self.cv_results,
            'best_params': self.best_params,
            'training_log': self.training_log.copy()
        }
        
        return summary 